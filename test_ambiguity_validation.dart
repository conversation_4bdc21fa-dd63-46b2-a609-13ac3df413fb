import 'lib/models/parse_result.dart';
import 'lib/models/transaction_model.dart';

void main() {
  // Test the AmbiguityType constants and validation
  print('Testing AmbiguityType validation...');
  
  // Valid ambiguity types
  print('Valid types: ${AmbiguityType.validTypes}');
  
  // Test validation
  print('Validating null: ${AmbiguityType.isValid(null)}'); // Should be true
  print('Validating "missing_amount": ${AmbiguityType.isValid(AmbiguityType.missingAmount)}'); // Should be true
  print('Validating "invalid_type": ${AmbiguityType.isValid("invalid_type")}'); // Should be false
  
  // Create a test transaction
  final testTransaction = Transaction(
    id: 'test-id',
    amount: 100.0,
    type: TransactionType.expense,
    categoryId: 'test-category',
    date: DateTime.now(),
    description: 'Test transaction',
    tags: [],
    currencyCode: 'USD',
  );
  
  // Test valid ambiguity type
  try {
    final validResult = ParseResult.success(
      testTransaction,
      ambiguityType: AmbiguityType.missingAmount,
    );
    print('✓ Successfully created ParseResult with valid ambiguityType: ${validResult.ambiguityType}');
  } catch (e) {
    print('✗ Unexpected error with valid ambiguityType: $e');
  }
  
  // Test invalid ambiguity type
  try {
    final invalidResult = ParseResult.success(
      testTransaction,
      ambiguityType: 'invalid_ambiguity_type',
    );
    print('✗ Should have thrown error for invalid ambiguityType');
  } catch (e) {
    print('✓ Correctly caught error for invalid ambiguityType: $e');
  }
  
  // Test null ambiguity type (should be valid)
  try {
    final nullResult = ParseResult.success(testTransaction);
    print('✓ Successfully created ParseResult with null ambiguityType');
  } catch (e) {
    print('✗ Unexpected error with null ambiguityType: $e');
  }
  
  print('\nValidation tests completed!');
}
