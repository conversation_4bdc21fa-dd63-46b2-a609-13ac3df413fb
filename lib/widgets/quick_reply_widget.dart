import 'package:flutter/material.dart';
import '../models/parse_result.dart';

/// Data class for quick reply options with enhanced features
class QuickReplyOption {
  final String text;
  final IconData? icon;
  final Color? backgroundColor;
  final Color? textColor;
  final bool isPrimary;
  final String? semanticLabel;

  const QuickReplyOption({
    required this.text,
    this.icon,
    this.backgroundColor,
    this.textColor,
    this.isPrimary = false,
    this.semanticLabel,
  });
}

/// A reusable widget that displays horizontal quick reply buttons for user interaction
class QuickReplyWidget extends StatelessWidget {
  final List<String> replyOptions;
  final List<QuickReplyOption>? enhancedOptions;
  final Function(String) onReplySelected;
  final EdgeInsets? padding;
  final double? spacing;
  final bool enabled;
  final bool showIcons;
  final bool groupByPriority;

  const QuickReplyWidget({
    super.key,
    required this.replyOptions,
    required this.onReplySelected,
    this.enhancedOptions,
    this.padding,
    this.spacing,
    this.enabled = true,
    this.showIcons = false,
    this.groupByPriority = false,
  });

  /// Factory constructor for ambiguity-aware quick replies
  factory QuickReplyWidget.forAmbiguityType({
    required String ambiguityType,
    required List<String> options,
    required Function(String) onReplySelected,
    bool enabled = true,
  }) {
    final enhancedOptions = _createEnhancedOptionsForAmbiguity(ambiguityType, options);

    return QuickReplyWidget(
      replyOptions: options,
      enhancedOptions: enhancedOptions,
      onReplySelected: onReplySelected,
      enabled: enabled,
      showIcons: true,
      groupByPriority: true,
    );
  }

  /// Factory constructor with icons support
  factory QuickReplyWidget.withIcons({
    required List<QuickReplyOption> options,
    required Function(String) onReplySelected,
    bool enabled = true,
    EdgeInsets? padding,
    double? spacing,
  }) {
    return QuickReplyWidget(
      replyOptions: options.map((opt) => opt.text).toList(),
      enhancedOptions: options,
      onReplySelected: onReplySelected,
      enabled: enabled,
      showIcons: true,
      padding: padding,
      spacing: spacing,
    );
  }

  @override
  Widget build(BuildContext context) {
    if (replyOptions.isEmpty) {
      return const SizedBox.shrink();
    }

    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Container(
      padding: padding ?? const EdgeInsets.symmetric(vertical: 8.0, horizontal: 4.0),
      child: Wrap(
        spacing: spacing ?? 8.0,
        runSpacing: 8.0,
        children: replyOptions.map((option) {
          return _buildReplyButton(
            context,
            option,
            colorScheme,
          );
        }).toList(),
      ),
    );
  }

  Widget _buildReplyButton(
    BuildContext context,
    String option,
    ColorScheme colorScheme,
  ) {
    return Material(
      child: InkWell(
        onTap: enabled ? () => onReplySelected(option) : null,
        borderRadius: BorderRadius.circular(20.0),
        child: Container(
          padding: const EdgeInsets.symmetric(
            horizontal: 16.0,
            vertical: 8.0,
          ),
          decoration: BoxDecoration(
            color: enabled 
                ? colorScheme.secondaryContainer
                : colorScheme.surfaceVariant.withOpacity(0.5),
            borderRadius: BorderRadius.circular(20.0),
            border: Border.all(
              color: enabled 
                  ? colorScheme.outline.withOpacity(0.3)
                  : colorScheme.outline.withOpacity(0.1),
              width: 1.0,
            ),
          ),
          child: Text(
            option,
            style: TextStyle(
              color: enabled 
                  ? colorScheme.onSecondaryContainer
                  : colorScheme.onSurfaceVariant.withOpacity(0.5),
              fontSize: 14.0,
              fontWeight: FontWeight.w500,
            ),
            semanticsLabel: 'Quick reply: $option',
          ),
        ),
      ),
    );
  }
}

/// A specialized quick reply widget for transaction type selection
class TransactionTypeQuickReply extends StatelessWidget {
  final Function(String) onTypeSelected;
  final bool enabled;

  const TransactionTypeQuickReply({
    super.key,
    required this.onTypeSelected,
    this.enabled = true,
  });

  @override
  Widget build(BuildContext context) {
    return QuickReplyWidget(
      replyOptions: const ['Expense', 'Income', 'Cancel'],
      onReplySelected: onTypeSelected,
      enabled: enabled,
    );
  }
}

/// A specialized quick reply widget for category selection
class CategoryQuickReply extends StatelessWidget {
  final List<String> categories;
  final Function(String) onCategorySelected;
  final bool enabled;

  const CategoryQuickReply({
    super.key,
    required this.categories,
    required this.onCategorySelected,
    this.enabled = true,
  });

  @override
  Widget build(BuildContext context) {
    final options = [...categories, 'Other', 'Cancel'];
    
    return QuickReplyWidget(
      replyOptions: options,
      onReplySelected: onCategorySelected,
      enabled: enabled,
    );
  }
}
